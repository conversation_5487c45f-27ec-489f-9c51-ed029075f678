<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>None.html</title>
    <style>
      html, body {
        box-sizing: border-box;
        display: flow-root;
        height: 100%;
        margin: 0;
        padding: 0;
      }
    </style>
    <script type="text/javascript" src="https://cdn.bokeh.org/bokeh/release/bokeh-3.7.3.min.js"></script>
    <script type="text/javascript">
        Bokeh.set_log_level("info");
    </script>
  </head>
  <body>
    <div id="ec5c5df4-71e7-49fc-9da9-4d9df69c544c" data-root-id="p1509" style="display: contents;"></div>
  
    <script type="application/json" id="f1582ee1-78aa-40f0-a685-29c516c0c0a5">
      {"da35abf5-6b54-450a-86a5-69b96120c05d":{"version":"3.7.3","title":"Bokeh Application","roots":[{"type":"object","name":"GridPlot","id":"p1509","attributes":{"rows":null,"cols":null,"sizing_mode":"stretch_width","toolbar":{"type":"object","name":"Toolbar","id":"p1508","attributes":{"tools":[{"type":"object","name":"ToolProxy","id":"p1497","attributes":{"tools":[{"type":"object","name":"PanTool","id":"p1376","attributes":{"dimensions":"width"}},{"type":"object","name":"PanTool","id":"p1423","attributes":{"dimensions":"width"}}]}},{"type":"object","name":"ToolProxy","id":"p1498","attributes":{"tools":[{"type":"object","name":"WheelZoomTool","id":"p1377","attributes":{"dimensions":"width","renderers":"auto","maintain_focus":false}},{"type":"object","name":"WheelZoomTool","id":"p1424","attributes":{"dimensions":"width","renderers":"auto","maintain_focus":false}}]}},{"type":"object","name":"ToolProxy","id":"p1499","attributes":{"tools":[{"type":"object","name":"WheelPanTool","id":"p1378"},{"type":"object","name":"WheelPanTool","id":"p1425"}]}},{"type":"object","name":"ToolProxy","id":"p1500","attributes":{"tools":[{"type":"object","name":"BoxZoomTool","id":"p1379","attributes":{"dimensions":"both","overlay":{"type":"object","name":"BoxAnnotation","id":"p1380","attributes":{"syncable":false,"line_color":"black","line_alpha":1.0,"line_width":2,"line_dash":[4,4],"fill_color":"lightgrey","fill_alpha":0.5,"level":"overlay","visible":false,"left":{"type":"number","value":"nan"},"right":{"type":"number","value":"nan"},"top":{"type":"number","value":"nan"},"bottom":{"type":"number","value":"nan"},"left_units":"canvas","right_units":"canvas","top_units":"canvas","bottom_units":"canvas","handles":{"type":"object","name":"BoxInteractionHandles","id":"p1386","attributes":{"all":{"type":"object","name":"AreaVisuals","id":"p1385","attributes":{"fill_color":"white","hover_fill_color":"lightgray"}}}}}}}},{"type":"object","name":"BoxZoomTool","id":"p1426","attributes":{"dimensions":"both","overlay":{"type":"object","name":"BoxAnnotation","id":"p1427","attributes":{"syncable":false,"line_color":"black","line_alpha":1.0,"line_width":2,"line_dash":[4,4],"fill_color":"lightgrey","fill_alpha":0.5,"level":"overlay","visible":false,"left":{"type":"number","value":"nan"},"right":{"type":"number","value":"nan"},"top":{"type":"number","value":"nan"},"bottom":{"type":"number","value":"nan"},"left_units":"canvas","right_units":"canvas","top_units":"canvas","bottom_units":"canvas","handles":{"type":"object","name":"BoxInteractionHandles","id":"p1433","attributes":{"all":{"type":"object","name":"AreaVisuals","id":"p1432","attributes":{"fill_color":"white","hover_fill_color":"lightgray"}}}}}}}}]}},{"type":"object","name":"ToolProxy","id":"p1501","attributes":{"tools":[{"type":"object","name":"UndoTool","id":"p1387"},{"type":"object","name":"UndoTool","id":"p1434"}]}},{"type":"object","name":"ToolProxy","id":"p1502","attributes":{"tools":[{"type":"object","name":"RedoTool","id":"p1388"},{"type":"object","name":"RedoTool","id":"p1435"}]}},{"type":"object","name":"ToolProxy","id":"p1503","attributes":{"tools":[{"type":"object","name":"ResetTool","id":"p1389"},{"type":"object","name":"ResetTool","id":"p1436"}]}},{"type":"object","name":"SaveTool","id":"p1504"},{"type":"object","name":"ToolProxy","id":"p1505","attributes":{"tools":[{"type":"object","name":"HoverTool","id":"p1492","attributes":{"renderers":[{"type":"object","name":"GlyphRenderer","id":"p1488","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p1391","attributes":{"selected":{"type":"object","name":"Selection","id":"p1392","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p1393"},"data":{"type":"map","entries":[["index",{"type":"ndarray","array":{"type":"bytes","data":"AAAAAAEAAAACAAAAAwAAAAQAAAAFAAAABgAAAAcAAAAIAAAACQAAAAoAAAALAAAADAAAAA0AAAAOAAAADwAAABAAAAARAAAAEgAAABMAAAAUAAAAFQAAABYAAAAXAAAAGAAAABkAAAAaAAAAGwAAABwAAAAdAAAAHgAAAB8AAAAgAAAAIQAAACIAAAAjAAAAJAAAACUAAAAmAAAAJwAAACgAAAApAAAAKgAAACsAAAAsAAAALQAAAC4AAAAvAAAAMAAAADEAAAAyAAAAMwAAADQAAAA1AAAANgAAADcAAAA4AAAAOQAAADoAAAA7AAAAPAAAAD0AAAA+AAAAPwAAAEAAAABBAAAAQgAAAEMAAABEAAAARQAAAEYAAABHAAAASAAAAEkAAABKAAAASwAAAEwAAABNAAAATgAAAE8AAABQAAAAUQAAAFIAAABTAAAAVAAAAFUAAABWAAAAVwAAAFgAAABZAAAAWgAAAFsAAABcAAAAXQAAAF4AAABfAAAAYAAAAGEAAABiAAAAYwAAAGQAAABlAAAAZgAAAGcAAABoAAAAaQAAAGoAAABrAAAAbAAAAG0AAABuAAAAbwAAAHAAAABxAAAAcgAAAHMAAAB0AAAAdQAAAHYAAAB3AAAAeAAAAHkAAAB6AAAAewAAAHwAAAB9AAAAfgAAAH8AAACAAAAAgQAAAIIAAACDAAAAhAAAAIUAAACGAAAAhwAAAIgAAACJAAAAigAAAIsAAACMAAAAjQAAAI4AAACPAAAAkAAAAJEAAACSAAAAkwAAAJQAAACVAAAAlgAAAJcAAACYAAAAmQAAAJoAAACbAAAAnAAAAJ0AAACeAAAAnwAAAKAAAAChAAAAogAAAKMAAACkAAAApQAAAKYAAACnAAAAqAAAAKkAAACqAAAAqwAAAKwAAACtAAAArgAAAK8AAACwAAAAsQAAALIAAACzAAAAtAAAALUAAAC2AAAAtwAAALgAAAC5AAAAugAAALsAAAC8AAAAvQAAAL4AAAC/AAAAwAAAAMEAAADCAAAAwwAAAMQAAADFAAAAxgAAAMcAAADIAAAAyQAAAMoAAADLAAAAzAAAAM0AAADOAAAAzwAAANAAAADRAAAA0gAAANMAAADUAAAA1QAAAA=="},"shape":[214],"dtype":"int32","order":"little"}],["Open",{"type":"ndarray","array":{"type":"bytes","data":"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"},"shape":[214],"dtype":"float64","order":"little"}],["High",{"type":"ndarray","array":{"type":"bytes","data":"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"},"shape":[214],"dtype":"float64","order":"little"}],["Low",{"type":"ndarray","array":{"type":"bytes","data":"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"},"shape":[214],"dtype":"float64","order":"little"}],["Close",{"type":"ndarray","array":{"type":"bytes","data":"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"},"shape":[214],"dtype":"float64","order":"little"}],["Volume",{"type":"ndarray","array":{"type":"bytes","data":"ggAAAFoAAABQAAAAMgAAAIIAAADSAAAA0gAAAFAAAAA8AAAACgAAADIAAAAeAAAAHgAAAB4AAAAyAAAACgAAAHgAAAAyAAAAHgAAADwAAAAyAAAAHgAAAB4AAAAeAAAAMgAAAFAAAABkAAAACgAAABQAAAAoAAAACgAAAFoAAAAKAAAAFAAAADwAAABaAAAAFAAAABQAAAAKAAAAPAAAAB4AAAB4AAAAggAAAG4AAABuAAAAZAAAAPAAAAB4AAAAUAAAAEYAAABGAAAAZAAAAEYAAABGAAAAHgAAAIIAAAAeAAAAUAAAACgAAAAUAAAAKAAAABQAAABaAAAACgAAAEYAAAA8AAAAPAAAAHgAAABuAAAAbgAAAB4AAAA8AAAAPAAAACgAAAAKAAAARgAAAJYAAABkAAAAWgAAACgAAACWAAAAKAAAAFoAAAAoAAAAWgAAAFAAAAAUAAAAWgAAAHgAAABuAAAAbgAAAKoAAABaAAAAWgAAAHgAAAAoAAAAFAAAAKoAAAAKAAAAvgAAANIAAABGAAAAZAAAAMgAAAC0AAAAWgAAAB4AAACCAAAAbgAAAIIAAABGAAAAWgAAAFoAAAAIAgAAhAMAACoDAAAcAgAAtAAAAMIBAAD6BQAAhAMAAGgBAAAcAgAAwgEAANACAAB2AgAAdAQAACoDAAD+AQAAHAIAALQAAAAOAQAApAEAAMIBAAAcAgAAKgMAALQAAAC0AAAAhAMAAMIBAADCAQAAaAEAALQAAABaAAAADgEAALQAAAC0AAAAaAEAAGgBAAAOAQAAtAAAAGgBAAAOAQAAaAEAALQAAAAcAgAAdgIAAGgBAABoAQAAtAAAAA4BAABoAQAAwAMAAMIBAAD+AQAARgUAAN4DAABoAQAAsgIAAJIEAADCAQAAwgEAAMIBAADeAwAA3gMAAMIBAADCAQAAKgMAAGgBAAC0AAAA3gMAABwCAADCAQAADgEAAMIBAAAqAwAAaAEAALQAAAC0AAAAdgIAALQAAAAOAQAAWgAAAMIBAAB2AgAAHAIAAN4DAADCAQAAWgAAALQAAAC0AAAAWgAAAFoAAABaAAAAtAAAAHYCAAAOAQAAWgAAAMIBAABoAQAAWgAAAGgBAAC0AAAAwgEAAA=="},"shape":[214],"dtype":"int32","order":"little"}],["datetime",{"type":"ndarray","array":{"type":"bytes","data":"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"},"shape":[214],"dtype":"float64","order":"little"}],["inc",{"type":"ndarray","array":["1","0","0","0","1","0","1","1","0","1","1","1","0","1","0","1","0","1","1","0","1","0","0","0","0","0","0","1","0","1","1","0","1","0","0","0","0","0","1","0","1","0","0","0","1","0","1","0","1","1","0","1","0","1","1","1","1","0","1","0","0","0","1","1","0","0","1","1","0","0","1","0","0","0","1","0","0","1","0","1","0","0","0","0","0","1","0","1","1","1","0","0","1","1","0","0","1","0","1","1","0","1","0","0","1","1","1","0","1","1","0","1","1","1","0","1","0","1","0","0","0","0","0","1","0","1","0","1","1","0","1","1","0","0","0","1","0","0","0","1","0","1","1","1","1","0","0","1","1","1","0","1","1","1","1","0","1","1","0","0","1","1","0","1","0","0","0","0","0","1","1","1","0","0","1","1","0","0","1","0","1","0","1","1","0","0","1","0","1","0","0","1","1","0","1","0","0","1","1","1","1","1","1","1","1","0","1","1","1","1","1","0","1","1"],"shape":[214],"dtype":"object","order":"little"}],["ohlc_low",{"type":"ndarray","array":{"type":"bytes","data":"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"},"shape":[214],"dtype":"float64","order":"little"}],["ohlc_high",{"type":"ndarray","array":{"type":"bytes","data":"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"},"shape":[214],"dtype":"float64","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p1489","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1490"}}},"glyph":{"type":"object","name":"VBar","id":"p1485","attributes":{"x":{"type":"field","field":"index"},"width":{"type":"value","value":0.8},"bottom":{"type":"field","field":"Close"},"top":{"type":"field","field":"Open"},"fill_color":{"type":"field","field":"inc","transform":{"type":"object","name":"CategoricalColorMapper","id":"p1397","attributes":{"palette":["tomato","lime"],"factors":["0","1"]}}}}},"nonselection_glyph":{"type":"object","name":"VBar","id":"p1486","attributes":{"x":{"type":"field","field":"index"},"width":{"type":"value","value":0.8},"bottom":{"type":"field","field":"Close"},"top":{"type":"field","field":"Open"},"line_alpha":{"type":"value","value":0.1},"fill_color":{"type":"field","field":"inc","transform":{"id":"p1397"}},"fill_alpha":{"type":"value","value":0.1},"hatch_alpha":{"type":"value","value":0.1}}},"muted_glyph":{"type":"object","name":"VBar","id":"p1487","attributes":{"x":{"type":"field","field":"index"},"width":{"type":"value","value":0.8},"bottom":{"type":"field","field":"Close"},"top":{"type":"field","field":"Open"},"line_alpha":{"type":"value","value":0.2},"fill_color":{"type":"field","field":"inc","transform":{"id":"p1397"}},"fill_alpha":{"type":"value","value":0.2},"hatch_alpha":{"type":"value","value":0.2}}}}}],"tooltips":[["Date","@datetime{%c}"],["x, y","$index\u00a0\u00a0\u00a0\u00a0$y{0,0.0[0000]}"],["OHLC","@Open{0,0.0[0000]}\u00a0\u00a0\u00a0\u00a0@High{0,0.0[0000]}\u00a0\u00a0\u00a0\u00a0@Low{0,0.0[0000]}\u00a0\u00a0\u00a0\u00a0@Close{0,0.0[0000]}"],["Volume","@Volume{0,0}"]],"formatters":{"type":"map","entries":[["@datetime","datetime"]]},"mode":"vline","point_policy":"follow_mouse"}}]}},{"type":"object","name":"ToolProxy","id":"p1506","attributes":{"tools":[{"type":"object","name":"HoverTool","id":"p1447","attributes":{"renderers":[{"type":"object","name":"GlyphRenderer","id":"p1444","attributes":{"data_source":{"id":"p1391"},"view":{"type":"object","name":"CDSView","id":"p1445","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1446"}}},"glyph":{"type":"object","name":"VBar","id":"p1441","attributes":{"x":{"type":"field","field":"index"},"width":{"type":"value","value":0.8},"top":{"type":"field","field":"Volume"},"line_color":{"type":"field","field":"inc","transform":{"id":"p1397"}},"fill_color":{"type":"field","field":"inc","transform":{"id":"p1397"}},"hatch_color":{"type":"field","field":"inc","transform":{"id":"p1397"}}}},"nonselection_glyph":{"type":"object","name":"VBar","id":"p1442","attributes":{"x":{"type":"field","field":"index"},"width":{"type":"value","value":0.8},"top":{"type":"field","field":"Volume"},"line_color":{"type":"field","field":"inc","transform":{"id":"p1397"}},"line_alpha":{"type":"value","value":0.1},"fill_color":{"type":"field","field":"inc","transform":{"id":"p1397"}},"fill_alpha":{"type":"value","value":0.1},"hatch_color":{"type":"field","field":"inc","transform":{"id":"p1397"}},"hatch_alpha":{"type":"value","value":0.1}}},"muted_glyph":{"type":"object","name":"VBar","id":"p1443","attributes":{"x":{"type":"field","field":"index"},"width":{"type":"value","value":0.8},"top":{"type":"field","field":"Volume"},"line_color":{"type":"field","field":"inc","transform":{"id":"p1397"}},"line_alpha":{"type":"value","value":0.2},"fill_color":{"type":"field","field":"inc","transform":{"id":"p1397"}},"fill_alpha":{"type":"value","value":0.2},"hatch_color":{"type":"field","field":"inc","transform":{"id":"p1397"}},"hatch_alpha":{"type":"value","value":0.2}}}}}],"tooltips":[["Date","@datetime{%c}"],["Volume","@Volume{0.00 a}"]],"formatters":{"type":"map","entries":[["@datetime","datetime"]]},"mode":"vline","point_policy":"follow_mouse"}}]}},{"type":"object","name":"ToolProxy","id":"p1507","attributes":{"tools":[{"type":"object","name":"CrosshairTool","id":"p1496","attributes":{"overlay":[{"type":"object","name":"Span","id":"p1494","attributes":{"line_dash":[2,4]}},{"type":"object","name":"Span","id":"p1495","attributes":{"dimension":"height","line_dash":[2,4]}}],"line_color":"lightgrey"}},{"id":"p1496"}]}}],"logo":null,"active_drag":{"id":"p1497"},"active_scroll":{"id":"p1498"}}},"toolbar_location":"right","children":[[{"type":"object","name":"Figure","id":"p1355","attributes":{"width":null,"height":400,"sizing_mode":"stretch_width","x_range":{"type":"object","name":"Range1d","id":"p1354","attributes":{"js_property_callbacks":{"type":"map","entries":[["change:end",[{"type":"object","name":"CustomJS","id":"p1493","attributes":{"args":{"type":"map","entries":[["ohlc_range",{"type":"object","name":"DataRange1d","id":"p1357"}],["source",{"id":"p1391"}],["volume_range",{"type":"object","name":"DataRange1d","id":"p1404"}]]},"code":"if (!window._bt_scale_range) {\n    window._bt_scale_range = function (range, min, max, pad) {\n        \"use strict\";\n        if (min !== Infinity &amp;&amp; max !== -Infinity) {\n            pad = pad ? (max - min) * .03 : 0;\n            range.start = min - pad;\n            range.end = max + pad;\n        } else console.error('backtesting: scale range error:', min, max, range);\n    };\n}\n\nclearTimeout(window._bt_autoscale_timeout);\n\nwindow._bt_autoscale_timeout = setTimeout(function () {\n    /**\n     * @variable cb_obj `fig_ohlc.x_range`.\n     * @variable source `ColumnDataSource`\n     * @variable ohlc_range `fig_ohlc.y_range`.\n     * @variable volume_range `fig_volume.y_range`.\n     */\n    \"use strict\";\n\n    let i = Math.max(Math.floor(cb_obj.start), 0),\n        j = Math.min(Math.ceil(cb_obj.end), source.data['ohlc_high'].length);\n\n    let max = Math.max.apply(null, source.data['ohlc_high'].slice(i, j)),\n        min = Math.min.apply(null, source.data['ohlc_low'].slice(i, j));\n    _bt_scale_range(ohlc_range, min, max, true);\n\n    if (volume_range) {\n        max = Math.max.apply(null, source.data['Volume'].slice(i, j));\n        _bt_scale_range(volume_range, 0, max * 1.03, false);\n    }\n\n}, 50);\n"}}]]]},"end":213,"bounds":[-10.65,223.65],"min_interval":10}},"y_range":{"id":"p1357"},"x_scale":{"type":"object","name":"LinearScale","id":"p1364"},"y_scale":{"type":"object","name":"LinearScale","id":"p1365"},"title":{"type":"object","name":"Title","id":"p1362"},"outline_line_color":"#666666","renderers":[{"type":"object","name":"GlyphRenderer","id":"p1458","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p1449","attributes":{"selected":{"type":"object","name":"Selection","id":"p1450","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p1451"},"data":{"type":"map","entries":[["index",{"type":"ndarray","array":{"type":"bytes","data":"AAAAAAAANEAAAAAAAGBRQAAAAAAAYF9AAAAAAABQZkAAAAAAACBqQA=="},"shape":[5],"dtype":"float64","order":"little"}],["Open",{"type":"ndarray","array":{"type":"bytes","data":"ZmZmZgZF0kCamZmZ2UPSQAAAAABAPtJAZmZmZsY70kBmZmZmhj7SQA=="},"shape":[5],"dtype":"float64","order":"little"}],["High",{"type":"ndarray","array":{"type":"bytes","data":"mpmZmRlG0kCamZmZ2UPSQDMzMzOzQNJAZmZmZgZA0kBmZmZmhj/SQA=="},"shape":[5],"dtype":"float64","order":"little"}],["Low",{"type":"ndarray","array":{"type":"bytes","data":"zczMzGxD0kAAAAAAYD3SQM3MzMwMOtJAZmZmZsY70kBmZmZmRj7SQA=="},"shape":[5],"dtype":"float64","order":"little"}],["Close",{"type":"ndarray","array":{"type":"bytes","data":"MzMzMxNE0kDNzMzMDD7SQGZmZmamO9JAZmZmZsY+0kBmZmZmxj7SQA=="},"shape":[5],"dtype":"float64","order":"little"}],["Volume",{"type":"ndarray","array":{"type":"bytes","data":"QgkAAGYSAAB2UgAAVGAAAEALAAA="},"shape":[5],"dtype":"int32","order":"little"}],["_width",{"type":"ndarray","array":{"type":"bytes","data":"MzMzMzNzREAzMzMzM/NMQDMzMzMz80pAMzMzMzPzSUDNzMzMzMwhQA=="},"shape":[5],"dtype":"float64","order":"little"}],["inc",{"type":"ndarray","array":["0","0","0","1","1"],"shape":[5],"dtype":"object","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p1459","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1460"}}},"glyph":{"type":"object","name":"Segment","id":"p1455","attributes":{"x0":{"type":"field","field":"index"},"y0":{"type":"field","field":"High"},"x1":{"type":"field","field":"index"},"y1":{"type":"field","field":"Low"},"line_color":{"type":"value","value":"#bbbbbb"}}},"nonselection_glyph":{"type":"object","name":"Segment","id":"p1456","attributes":{"x0":{"type":"field","field":"index"},"y0":{"type":"field","field":"High"},"x1":{"type":"field","field":"index"},"y1":{"type":"field","field":"Low"},"line_color":{"type":"value","value":"#bbbbbb"},"line_alpha":{"type":"value","value":0.1}}},"muted_glyph":{"type":"object","name":"Segment","id":"p1457","attributes":{"x0":{"type":"field","field":"index"},"y0":{"type":"field","field":"High"},"x1":{"type":"field","field":"index"},"y1":{"type":"field","field":"Low"},"line_color":{"type":"value","value":"#bbbbbb"},"line_alpha":{"type":"value","value":0.2}}}}},{"type":"object","name":"GlyphRenderer","id":"p1468","attributes":{"data_source":{"id":"p1449"},"view":{"type":"object","name":"CDSView","id":"p1469","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1470"}}},"glyph":{"type":"object","name":"VBar","id":"p1465","attributes":{"x":{"type":"field","field":"index"},"width":{"type":"field","field":"_width"},"bottom":{"type":"field","field":"Close"},"top":{"type":"field","field":"Open"},"line_color":{"type":"value","value":null},"fill_color":{"type":"field","field":"inc","transform":{"type":"object","name":"CategoricalColorMapper","id":"p1461","attributes":{"palette":["rgb(254, 220, 214)","rgb(214, 254, 214)"],"factors":["0","1"]}}}}},"nonselection_glyph":{"type":"object","name":"VBar","id":"p1466","attributes":{"x":{"type":"field","field":"index"},"width":{"type":"field","field":"_width"},"bottom":{"type":"field","field":"Close"},"top":{"type":"field","field":"Open"},"line_color":{"type":"value","value":null},"line_alpha":{"type":"value","value":0.1},"fill_color":{"type":"field","field":"inc","transform":{"id":"p1461"}},"fill_alpha":{"type":"value","value":0.1},"hatch_alpha":{"type":"value","value":0.1}}},"muted_glyph":{"type":"object","name":"VBar","id":"p1467","attributes":{"x":{"type":"field","field":"index"},"width":{"type":"field","field":"_width"},"bottom":{"type":"field","field":"Close"},"top":{"type":"field","field":"Open"},"line_color":{"type":"value","value":null},"line_alpha":{"type":"value","value":0.2},"fill_color":{"type":"field","field":"inc","transform":{"id":"p1461"}},"fill_alpha":{"type":"value","value":0.2},"hatch_alpha":{"type":"value","value":0.2}}}}},{"type":"object","name":"GlyphRenderer","id":"p1477","attributes":{"data_source":{"id":"p1391"},"view":{"type":"object","name":"CDSView","id":"p1478","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1479"}}},"glyph":{"type":"object","name":"Segment","id":"p1474","attributes":{"x0":{"type":"field","field":"index"},"y0":{"type":"field","field":"High"},"x1":{"type":"field","field":"index"},"y1":{"type":"field","field":"Low"}}},"nonselection_glyph":{"type":"object","name":"Segment","id":"p1475","attributes":{"x0":{"type":"field","field":"index"},"y0":{"type":"field","field":"High"},"x1":{"type":"field","field":"index"},"y1":{"type":"field","field":"Low"},"line_alpha":{"type":"value","value":0.1}}},"muted_glyph":{"type":"object","name":"Segment","id":"p1476","attributes":{"x0":{"type":"field","field":"index"},"y0":{"type":"field","field":"High"},"x1":{"type":"field","field":"index"},"y1":{"type":"field","field":"Low"},"line_alpha":{"type":"value","value":0.2}}}}},{"id":"p1488"}],"toolbar":{"type":"object","name":"Toolbar","id":"p1363","attributes":{"tools":[{"id":"p1376"},{"id":"p1377"},{"id":"p1378"},{"id":"p1379"},{"id":"p1387"},{"id":"p1388"},{"id":"p1389"},{"type":"object","name":"SaveTool","id":"p1390"},{"id":"p1492"},{"id":"p1496"}],"active_drag":{"id":"p1376"},"active_scroll":{"id":"p1377"}}},"toolbar_location":null,"left":[{"type":"object","name":"LinearAxis","id":"p1371","attributes":{"ticker":{"type":"object","name":"BasicTicker","id":"p1372","attributes":{"mantissas":[1,2,5]}},"formatter":{"type":"object","name":"BasicTickFormatter","id":"p1373"},"major_label_policy":{"type":"object","name":"AllLabels","id":"p1374"}}}],"below":[{"type":"object","name":"LinearAxis","id":"p1366","attributes":{"visible":false,"ticker":{"type":"object","name":"BasicTicker","id":"p1367","attributes":{"mantissas":[1,2,5]}},"formatter":{"type":"object","name":"CustomJSTickFormatter","id":"p1401","attributes":{"args":{"type":"map","entries":[["axis",{"id":"p1366"}],["formatter",{"type":"object","name":"DatetimeTickFormatter","id":"p1400","attributes":{"days":"%a, %d %b"}}],["source",{"id":"p1391"}]]},"code":"\nthis.labels = this.labels || formatter.doFormat(ticks\n                                                .map(i =&gt; source.data.datetime[i])\n                                                .filter(t =&gt; t !== undefined));\nreturn this.labels[index] || \"\";\n        "}},"major_label_policy":{"type":"object","name":"AllLabels","id":"p1369"}}}],"center":[{"type":"object","name":"Grid","id":"p1370","attributes":{"axis":{"id":"p1366"}}},{"type":"object","name":"Grid","id":"p1375","attributes":{"dimension":1,"axis":{"id":"p1371"}}},{"type":"object","name":"Legend","id":"p1480","attributes":{"location":"top_left","border_line_color":"#333333","background_fill_alpha":0.9,"click_policy":"hide","label_text_font_size":"8pt","margin":0,"padding":5,"spacing":0,"items":[{"type":"object","name":"LegendItem","id":"p1481","attributes":{"label":{"type":"value","value":"OHLC"},"renderers":[{"id":"p1477"},{"id":"p1488"}]}}]}},{"type":"object","name":"Label","id":"p1491","attributes":{"text":"Created with Backtesting.py: http://kernc.github.io/backtesting.py","text_color":"silver","text_alpha":0.09,"x":10,"y":15,"x_units":"screen","y_units":"screen"}}],"min_border_top":3,"min_border_bottom":6,"min_border_left":0,"min_border_right":10}},0,0],[{"type":"object","name":"Figure","id":"p1402","attributes":{"width":null,"height":70,"sizing_mode":"stretch_width","x_range":{"id":"p1354"},"y_range":{"id":"p1404"},"x_scale":{"type":"object","name":"LinearScale","id":"p1411"},"y_scale":{"type":"object","name":"LinearScale","id":"p1412"},"title":{"type":"object","name":"Title","id":"p1409"},"outline_line_color":"#666666","renderers":[{"id":"p1444"}],"toolbar":{"type":"object","name":"Toolbar","id":"p1410","attributes":{"tools":[{"id":"p1423"},{"id":"p1424"},{"id":"p1425"},{"id":"p1426"},{"id":"p1434"},{"id":"p1435"},{"id":"p1436"},{"type":"object","name":"SaveTool","id":"p1437"},{"id":"p1447"},{"id":"p1496"}],"active_drag":{"id":"p1423"},"active_scroll":{"id":"p1424"}}},"toolbar_location":null,"left":[{"type":"object","name":"LinearAxis","id":"p1418","attributes":{"ticker":{"type":"object","name":"BasicTicker","id":"p1419","attributes":{"desired_num_ticks":3,"mantissas":[1,2,5]}},"formatter":{"type":"object","name":"NumeralTickFormatter","id":"p1448","attributes":{"format":"0 a"}},"axis_label":"Volume","major_label_policy":{"type":"object","name":"AllLabels","id":"p1421"},"minor_tick_line_color":null}}],"below":[{"type":"object","name":"LinearAxis","id":"p1413","attributes":{"visible":true,"ticker":{"type":"object","name":"BasicTicker","id":"p1414","attributes":{"mantissas":[1,2,5]}},"formatter":{"id":"p1401"},"major_label_policy":{"type":"object","name":"AllLabels","id":"p1416"}}}],"center":[{"type":"object","name":"Grid","id":"p1417","attributes":{"axis":{"id":"p1413"}}},{"type":"object","name":"Grid","id":"p1422","attributes":{"dimension":1,"axis":{"id":"p1418"}}}],"min_border_top":3,"min_border_bottom":6,"min_border_left":0,"min_border_right":10}},1,0]]}}],"callbacks":{"type":"map","entries":[["document_ready",[{"type":"object","name":"CustomJS","id":"p1353","attributes":{"code":"(function() { var i = document.createElement('iframe'); i.style.display='none';i.width=i.height=1;i.loading='eager';i.src='https://kernc.github.io/backtesting.py/plx.gif.html?utm_source='+location.origin;document.body.appendChild(i);})();"}}]]]}}}
    </script>
    <script type="text/javascript">
      (function() {
        const fn = function() {
          Bokeh.safely(function() {
            (function(root) {
              function embed_document(root) {
              const docs_json = document.getElementById('f1582ee1-78aa-40f0-a685-29c516c0c0a5').textContent;
              const render_items = [{"docid":"da35abf5-6b54-450a-86a5-69b96120c05d","roots":{"p1509":"ec5c5df4-71e7-49fc-9da9-4d9df69c544c"},"root_ids":["p1509"]}];
              root.Bokeh.embed.embed_items(docs_json, render_items);
              }
              if (root.Bokeh !== undefined) {
                embed_document(root);
              } else {
                let attempts = 0;
                const timer = setInterval(function(root) {
                  if (root.Bokeh !== undefined) {
                    clearInterval(timer);
                    embed_document(root);
                  } else {
                    attempts++;
                    if (attempts > 100) {
                      clearInterval(timer);
                      console.log("Bokeh: ERROR: Unable to run BokehJS code because BokehJS library is missing");
                    }
                  }
                }, 10, root)
              }
            })(window);
          });
        };
        if (document.readyState != "loading") fn();
        else document.addEventListener("DOMContentLoaded", fn);
      })();
    </script>
  </body>
</html>