#!/usr/bin/env python3

#!/usr/bin/env python3
"""
Comprehensive test suite for FileGenerator module
UNBREAKABLE RULE: Uses ONLY real market data from /tests/RealTestData
"""

import unittest
import pandas as pd
import os
import sys
import tempfile
import shutil
from unittest.mock import patch, MagicMock, Mock
from datetime import datetime

# Add src directory to path
src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../src'))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

import file_generator
from file_generator import FileGenerator

class TestFileGenerator(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """Load real market data - UNBREAKABLE RULE: REAL DATA ONLY"""
        data_path = os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_500_bars.csv')
        if not os.path.exists(data_path):
            raise FileNotFoundError("UNBREAKABLE RULE VIOLATION: Real market data file not found")
        
        # Read and process real market data
        cls.data = pd.read_csv(data_path)
        cls.data = cls.data.drop_duplicates()
        
        # Ensure proper OHLC capitalization
        for proper in ['Open', 'High', 'Low', 'Close', 'Volume']:
            for col in cls.data.columns:
                if col.lower() == proper.lower():
                    cls.data.rename(columns={col: proper}, inplace=True)
        
        # Handle DateTime column
        if 'DateTime' in cls.data.columns:
            cls.data['DateTime'] = pd.to_datetime(cls.data['DateTime'])
            cls.data.set_index('DateTime', inplace=True)
        elif 'Date' in cls.data.columns and 'Time' in cls.data.columns:
            cls.data['DateTime'] = pd.to_datetime(cls.data['Date'].astype(str) + ' ' + cls.data['Time'].astype(str))
            cls.data.set_index('DateTime', inplace=True)
        
        # Select subset for testing
        cls.sample_data = cls.data[['Open', 'High', 'Low', 'Close', 'Volume']].head(100)
        
        # Verify data integrity
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_cols:
            if col not in cls.sample_data.columns:
                raise AssertionError(f"UNBREAKABLE RULE VIOLATION: Missing required column: {col}")
        
        if cls.sample_data[['Open','High','Low','Close']].isnull().any().any():
            raise AssertionError('UNBREAKABLE RULE VIOLATION: NaN found in OHLC columns')

    def setUp(self):
        """Set up test environment"""
        # Create temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
        # Mock config to use test directory
        self.config_patcher = patch('file_generator.config')
        self.mock_config = self.config_patcher.start()
        self.mock_config.RESULTS_DIR = self.test_dir
        
        # Create FileGenerator instance
        self.file_gen = file_generator.FileGenerator()
        
        # Sample cortex results using real data
        self.sample_cortex_results = {
            'symbol': 'DAX',
            'llm_analysis': 'Test LLM analysis content',
            'mt4_ea_code': '// Test MT4 EA code',
            'ea_name': 'TestEA',
            'ohlc_data': self.sample_data
        }
        
    def tearDown(self):
        """Clean up test environment"""
        self.config_patcher.stop()
        # Clean up test directory
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_import(self):
        """Test file_generator module can be imported successfully"""
        try:
            import file_generator
            self.assertIsNotNone(file_generator)
        except Exception as e:
            self.fail(f'Import failed: {e}')

    def test_file_generator_initialization(self):
        """Test FileGenerator class initialization"""
        self.assertIsInstance(self.file_gen, file_generator.FileGenerator)
        self.assertEqual(self.file_gen.results_dir, self.test_dir)
        self.assertTrue(os.path.exists(self.test_dir))

    def test_generate_trading_system_files_complete(self):
        """Test complete trading system file generation"""
        # Create sample cortex results
        cortex_results = {
            'symbol': 'DAX',
            'llm_analysis': 'Test LLM analysis for DAX patterns',
            'mt4_ea_code': '//+------------------------------------------------------------------+\n//| Test EA |\n//+------------------------------------------------------------------+',
            'ea_name': 'TestEA',
            'ohlc_data': self.sample_data
        }
        
        # Create sample backtest results
        mock_stats = MagicMock()
        mock_stats.get.side_effect = lambda key, default=0: {
            'Return [%]': 15.5,
            'Return (Ann.) [%]': 18.2,
            'Max. Drawdown [%]': -8.5,
            'Sharpe Ratio': 1.25,
            '# Trades': 25,
            'Win Rate [%]': 65.0,
            'Start': 100000,
            'Equity Final [$]': 115500
        }.get(key, default)
        
        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]
        
        # Create sample validation results
        validation_results = {
            'success': True,
            'profitable_patterns': [{
                'pattern_id': 1,
                'entry_logic': 'Test entry logic',
                'exit_logic': 'Test exit logic'
            }]
        }
        
        # Mock the hardcoded converter
        with patch('mt4_hardcoded_converter.convert_profitable_patterns_to_mt4') as mock_converter:
            mock_converter.return_value = '//Test MT4 EA Code'
            
            result = self.file_gen.generate_trading_system_files(
                cortex_results, backtest_results, validation_results
            )
        
        # Verify result structure
        self.assertIsInstance(result, dict)
        self.assertIn('system_folder', result)
        self.assertIn('timestamp', result)
        self.assertIn('trading_system_report', result)
        self.assertIn('mt4_ea_files', result)
        
        # Verify files were created
        self.assertTrue(os.path.exists(result['system_folder']))
        self.assertTrue(os.path.exists(result['trading_system_report']))
        self.assertIsInstance(result['mt4_ea_files'], list)
        self.assertEqual(len(result['mt4_ea_files']), 1)
        self.assertTrue(os.path.exists(result['mt4_ea_files'][0]))
        
        # Verify folder naming
        folder_name = os.path.basename(result['system_folder'])
        self.assertTrue(folder_name.startswith('DAX_'))

    def test_generate_trading_system_files_no_backtest(self):
        """Test trading system file generation without backtest results"""
        cortex_results = {
            'symbol': 'DAX',
            'llm_analysis': 'Test analysis',
            'mt4_ea_code': '//Test EA',
            'ea_name': 'TestEA',
            'ohlc_data': self.sample_data
        }
        
        validation_results = {
            'success': True,
            'profitable_patterns': [{'pattern_id': 1}]
        }
        
        with patch('mt4_hardcoded_converter.convert_profitable_patterns_to_mt4') as mock_converter:
            mock_converter.return_value = '//Test MT4 EA Code'
            
            result = self.file_gen.generate_trading_system_files(
                cortex_results, None, validation_results
            )
        
        self.assertIsInstance(result, dict)
        self.assertIn('system_folder', result)
        self.assertTrue(os.path.exists(result['system_folder']))

    def test_generate_trading_system_report(self):
        """Test trading system report generation"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        symbol = 'DAX'
        llm_analysis = 'Test LLM analysis with detailed patterns'
        timestamp = '20231201_120000'
        
        # Create mock backtest results
        mock_stats = MagicMock()
        mock_stats.get.side_effect = lambda key, default=0: {
            'Return [%]': 12.5,
            'Return (Ann.) [%]': 15.0,
            'Max. Drawdown [%]': -5.2,
            'Sharpe Ratio': 1.8,
            '# Trades': 15,
            'Win Rate [%]': 73.3,
            'Start': 100000,
            'Equity Final [$]': 112500
        }.get(key, default)
        # Configure the mock to not have an 'empty' attribute or make it False
        mock_stats.empty = False
        
        backtest_results = [{'backtesting_py_stats': mock_stats}]
        
        result = self.file_gen._generate_trading_system_report(
            folder, symbol, llm_analysis, backtest_results, timestamp
        )
        
        self.assertIsNotNone(result)
        self.assertTrue(os.path.exists(result))
        
        # Verify file content
        with open(result, 'r') as f:
            content = f.read()
            self.assertIn('JAEGER TRADING SYSTEM - DAX', content)
            self.assertIn('Test LLM analysis', content)
            self.assertIn('backtesting.py', content)
            self.assertIn('Total Return', content)

    def test_generate_trading_system_report_no_backtest(self):
        """Test trading system report generation without backtest results"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        result = self.file_gen._generate_trading_system_report(
            folder, 'DAX', 'Test analysis', None, '20231201_120000'
        )
        
        self.assertIsNotNone(result)
        self.assertTrue(os.path.exists(result))
        
        with open(result, 'r') as f:
            content = f.read()
            self.assertIn('No backtesting results provided', content)

    def test_format_backtesting_py_stats(self):
        """Test formatting of backtesting.py statistics"""
        # Test with valid stats
        mock_stats = MagicMock()
        mock_stats.get.side_effect = lambda key, default=0: {
            'Return [%]': 20.5,
            'Return (Ann.) [%]': 25.0,
            'Max. Drawdown [%]': -12.3,
            'Sharpe Ratio': 1.45,
            '# Trades': 30,
            'Win Rate [%]': 68.5,
            'Start': 100000,
            'Equity Final [$]': 120500
        }.get(key, default)
        
        result = self.file_gen._format_backtesting_py_stats(mock_stats)
        
        self.assertIsInstance(result, str)
        self.assertIn('COMPREHENSIVE PERFORMANCE METRICS', result)
        self.assertIn('20.50%', result)
        self.assertIn('1.450', result)
        self.assertIn('30', result)
        
        # Test with invalid stats
        result = self.file_gen._format_backtesting_py_stats("invalid")
        self.assertIn('Invalid stats format', result)
        
        # Test with zero trades
        mock_stats_zero = MagicMock()
        mock_stats_zero.get.return_value = 0
        result = self.file_gen._format_backtesting_py_stats(mock_stats_zero)
        self.assertIn('No trades executed', result)

    @patch('mt4_hardcoded_converter.convert_profitable_patterns_to_mt4')
    def test_generate_validated_mt4_ea_success(self, mock_converter):
        """Test successful MT4 EA generation from validated patterns"""
        mock_converter.return_value = '//Test MT4 EA Code\nint OnInit() { return INIT_SUCCEEDED; }'
        
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        validation_results = {
            'success': True,
            'profitable_patterns': [{
                'pattern_id': 1,
                'entry_logic': 'Test entry',
                'exit_logic': 'Test exit'
            }]
        }
        
        result = self.file_gen._generate_validated_mt4_ea(
            folder, 'TestEA', validation_results
        )
        
        self.assertIsNotNone(result)
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        self.assertTrue(os.path.exists(result[0]))
        self.assertTrue(result[0].endswith('.mq4'))
        
        # Verify file content
        with open(result[0], 'r') as f:
            content = f.read()
            self.assertIn('Test MT4 EA Code', content)
        
        mock_converter.assert_called_once()

    def test_generate_validated_mt4_ea_no_validation(self):
        """Test MT4 EA generation with no validation results"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        with patch.object(self.file_gen, '_save_empty_mt4_ea') as mock_empty:
            mock_empty.return_value = os.path.join(folder, 'TestEA.mq4')
            
            result = self.file_gen._generate_validated_mt4_ea(
                folder, 'TestEA', None
            )
            
            mock_empty.assert_called_once_with(folder, 'TestEA')

    def test_generate_validated_mt4_ea_no_profitable_patterns(self):
        """Test MT4 EA generation with no profitable patterns"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        validation_results = {
            'success': True,
            'profitable_patterns': []
        }
        
        with patch.object(self.file_gen, '_save_empty_mt4_ea') as mock_empty:
            mock_empty.return_value = os.path.join(folder, 'TestEA.mq4')
            
            result = self.file_gen._generate_validated_mt4_ea(
                folder, 'TestEA', validation_results
            )
            
            mock_empty.assert_called_once_with(folder, 'TestEA')

    @patch('mt4_hardcoded_converter.HardcodedMT4Converter')
    def test_save_empty_mt4_ea(self, mock_converter_class):
        """Test saving empty MT4 EA"""
        mock_converter = MagicMock()
        mock_converter._generate_empty_ea.return_value = '//Empty EA Code'
        mock_converter_class.return_value = mock_converter
        
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        result = self.file_gen._save_empty_mt4_ea(folder, 'EmptyEA')

        self.assertIsNotNone(result)
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        self.assertTrue(os.path.exists(result[0]))

        with open(result[0], 'r') as f:
            content = f.read()
            self.assertIn('Empty EA Code', content)

    def test_save_mt4_ea_file(self):
        """Test saving MT4 EA file"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        ea_code = '//Test EA Code\nint OnInit() { return INIT_SUCCEEDED; }'
        
        result = self.file_gen._save_mt4_ea_file(folder, 'TestEA', ea_code)
        
        self.assertIsNotNone(result)
        self.assertTrue(os.path.exists(result))
        
        with open(result, 'r') as f:
            content = f.read()
            self.assertEqual(content, ea_code)

    def test_save_mt4_ea_file_no_code(self):
        """Test saving MT4 EA file with no code"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        result = self.file_gen._save_mt4_ea_file(folder, 'TestEA', '')
        self.assertIsNone(result)
        
        result = self.file_gen._save_mt4_ea_file(folder, 'TestEA', None)
        self.assertIsNone(result)

    def test_save_mt4_ea_file_backup_existing(self):
        """Test backing up existing MT4 EA file"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        ea_file = os.path.join(folder, 'TestEA.mq4')
        
        # Create existing file
        with open(ea_file, 'w') as f:
            f.write('//Old EA Code')
        
        # Save new file
        new_code = '//New EA Code'
        result = self.file_gen._save_mt4_ea_file(folder, 'TestEA', new_code)
        
        self.assertIsNotNone(result)
        
        # Verify new content
        with open(result, 'r') as f:
            content = f.read()
            self.assertEqual(content, new_code)
        
        # Verify backup was created
        backup_files = [f for f in os.listdir(folder) if f.startswith('TestEA.mq4.bak_')]
        self.assertEqual(len(backup_files), 1)

    @patch('chart_html_generator.HTMLChartGenerator')
    def test_generate_html_charts(self, mock_chart_gen_class):
        """Test HTML chart generation"""
        mock_chart_gen = MagicMock()
        mock_chart_gen.generate_backtest_html_chart.return_value = True
        mock_chart_gen_class.return_value = mock_chart_gen
        
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        # Create mock stats without plot method
        mock_stats = MagicMock()
        mock_stats.empty = False
        del mock_stats.plot  # Remove plot method to test fallback
        
        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]
        
        # Mock file creation
        chart_file = os.path.join(folder, 'DAX_pattern_1_chart.html')
        
        def mock_generate_chart(*args, **kwargs):
            # Create the file to simulate successful generation
            with open(chart_file, 'w') as f:
                f.write('<html>Test Chart</html>')
            return True
        
        mock_chart_gen.generate_backtest_html_chart.side_effect = mock_generate_chart
        
        result = self.file_gen._generate_html_charts(
            folder, 'DAX', self.sample_data, backtest_results
        )
        
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        self.assertTrue(os.path.exists(result[0]))

    def test_generate_html_charts_no_backtest(self):
        """Test HTML chart generation with no backtest results"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        result = self.file_gen._generate_html_charts(
            folder, 'DAX', self.sample_data, None
        )
        
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 0)

    @patch('file_generator.HTMLChartGenerator')
    @unittest.skip("Test hangs due to HTMLChartGenerator import issue - needs investigation")
    def test_generate_html_charts_with_plot_method(self, mock_chart_gen_class):
        """Test HTML chart generation using backtesting.py plot method"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)

        # Create mock stats with plot method and _trades attribute
        mock_stats = MagicMock()
        mock_stats.empty = False

        # Add _trades attribute with non-empty DataFrame to trigger plot method
        mock_trades = MagicMock()
        mock_trades.empty = False
        mock_stats._trades = mock_trades

        chart_file = os.path.join(folder, 'DAX_pattern_1_chart.html')

        def mock_plot(*args, **kwargs):
            # Create the file to simulate successful plot
            with open(chart_file, 'w') as f:
                f.write('<html>Backtesting.py Chart</html>')
            return MagicMock()

        mock_stats.plot = mock_plot

        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]

        result = self.file_gen._generate_html_charts(
            folder, 'DAX', self.sample_data, backtest_results
        )

        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        self.assertTrue(os.path.exists(result[0]))

    def test_generate_trade_csv_files(self):
        """Test trade CSV file generation"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        # Create mock trades DataFrame
        trades_data = {
            'EntryTime': ['2023-01-01 10:00:00', '2023-01-02 14:30:00'],
            'ExitTime': ['2023-01-01 16:00:00', '2023-01-02 18:45:00'],
            'Direction': ['Long', 'Short'],
            'EntryPrice': [100.0, 105.0],
            'ExitPrice': [102.0, 103.0],
            'Profit': [200.0, 200.0],
            'Size': [1.0, 1.0],
            'StopLoss': [98.0, 107.0],
            'TakeProfit': [104.0, 101.0]
        }
        trades_df = pd.DataFrame(trades_data)
        
        mock_stats = MagicMock()
        mock_stats.empty = False
        mock_stats._trades = trades_df
        
        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]
        
        result = self.file_gen._generate_trade_csv_files(
            folder, 'DAX', backtest_results
        )
        
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        self.assertTrue(os.path.exists(result[0]))
        
        # Verify CSV content
        df = pd.read_csv(result[0])
        self.assertEqual(len(df), 2)
        self.assertIn('EntryTime', df.columns)
        self.assertIn('Profit', df.columns)

    def test_generate_trade_csv_files_no_trades(self):
        """Test trade CSV file generation with no trades"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        # Create mock stats with empty trades
        mock_stats = MagicMock()
        mock_stats.empty = False
        mock_stats._trades = pd.DataFrame()  # Empty DataFrame
        
        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]
        
        result = self.file_gen._generate_trade_csv_files(
            folder, 'DAX', backtest_results
        )
        
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 0)

    def test_generate_trade_csv_files_no_backtest(self):
        """Test trade CSV file generation with no backtest results"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        result = self.file_gen._generate_trade_csv_files(
            folder, 'DAX', None
        )
        
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 0)

    def test_generate_trade_csv_backup_existing(self):
        """Test backing up existing trade CSV file"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        csv_file = os.path.join(folder, 'DAX_pattern_1_trades.csv')
        
        # Create existing file
        with open(csv_file, 'w') as f:
            f.write('EntryTime,ExitTime,Profit\n2023-01-01,2023-01-01,100')
        
        # Create new trades data
        trades_data = {
            'EntryTime': ['2023-01-02 10:00:00'],
            'ExitTime': ['2023-01-02 16:00:00'],
            'Direction': ['Long'],
            'EntryPrice': [100.0],
            'ExitPrice': [102.0],
            'Profit': [200.0],
            'Size': [1.0],
            'StopLoss': [98.0],
            'TakeProfit': [104.0]
        }
        trades_df = pd.DataFrame(trades_data)
        
        mock_stats = MagicMock()
        mock_stats.empty = False
        mock_stats._trades = trades_df
        
        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]
        
        result = self.file_gen._generate_trade_csv_files(
            folder, 'DAX', backtest_results
        )
        
        self.assertEqual(len(result), 1)
        
        # Verify backup was created
        backup_files = [f for f in os.listdir(folder) if f.startswith('DAX_pattern_1_trades.csv.bak_')]
        self.assertEqual(len(backup_files), 1)

    def test_edge_cases_and_error_handling(self):
        """Test edge cases and error handling"""
        # Test with invalid folder path
        invalid_folder = '/nonexistent/path'
        
        # This should handle the error gracefully
        result = self.file_gen._generate_trading_system_report(
            invalid_folder, 'DAX', 'Test', None, '20231201_120000'
        )
        self.assertIsNone(result)
        
        # Test with missing required data
        cortex_results = {}  # Empty results
        
        result = self.file_gen.generate_trading_system_files(
            cortex_results, None, None
        )
        
        self.assertIsInstance(result, dict)
        self.assertIn('system_folder', result)

    def test_generate_validated_mt4_ea_file_write_error(self):
        """Test MT4 EA generation with file write error"""
        folder = '/nonexistent/invalid/path'
        validation_results = {
            'success': True,
            'profitable_patterns': [{'pattern_id': 1}]
        }
        
        with patch('mt4_hardcoded_converter.convert_profitable_patterns_to_mt4') as mock_converter:
            mock_converter.return_value = '//Test EA Code'
            
            result = self.file_gen._generate_validated_mt4_ea(
                folder, 'TestEA', validation_results
            )
            
            self.assertIsNone(result)

    def test_save_empty_mt4_ea_file_write_error(self):
        """Test empty MT4 EA generation with file write error"""
        folder = '/nonexistent/invalid/path'
        
        with patch('mt4_hardcoded_converter.HardcodedMT4Converter') as mock_converter_class:
            mock_converter = MagicMock()
            mock_converter._generate_empty_ea.return_value = '//Empty EA'
            mock_converter_class.return_value = mock_converter
            
            result = self.file_gen._save_empty_mt4_ea(folder, 'EmptyEA')
            
            self.assertIsNone(result)

    def test_save_mt4_ea_file_write_error(self):
        """Test MT4 EA file save with write error"""
        folder = '/nonexistent/invalid/path'
        ea_code = '//Test EA Code'
        
        result = self.file_gen._save_mt4_ea_file(folder, 'TestEA', ea_code)
        
        self.assertIsNone(result)

    def test_generate_html_charts_plot_method_failure(self):
        """Test HTML chart generation when plot method fails"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        # Create mock stats with plot method that raises exception
        mock_stats = MagicMock()
        mock_stats.empty = False
        
        def failing_plot(*args, **kwargs):
            raise Exception("Plot method failed")
        
        mock_stats.plot = failing_plot
        
        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]
        
        with patch('chart_html_generator.HTMLChartGenerator') as mock_chart_gen_class:
            mock_chart_gen = MagicMock()
            mock_chart_gen.generate_backtest_html_chart.return_value = False
            mock_chart_gen_class.return_value = mock_chart_gen
            
            result = self.file_gen._generate_html_charts(
                folder, 'DAX', self.sample_data, backtest_results
            )
            
            self.assertIsInstance(result, list)
            self.assertEqual(len(result), 0)

    def test_generate_html_charts_chart_gen_exception(self):
        """Test HTML chart generation with chart generator exception"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        mock_stats = MagicMock()
        mock_stats.empty = False
        del mock_stats.plot  # No plot method
        
        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]
        
        with patch('chart_html_generator.HTMLChartGenerator') as mock_chart_gen_class:
            mock_chart_gen = MagicMock()
            mock_chart_gen.generate_backtest_html_chart.side_effect = Exception("Chart generation failed")
            mock_chart_gen_class.return_value = mock_chart_gen
            
            result = self.file_gen._generate_html_charts(
                folder, 'DAX', self.sample_data, backtest_results
            )
            
            self.assertIsInstance(result, list)
            self.assertEqual(len(result), 0)

    def test_generate_trade_csv_missing_columns(self):
        """Test trade CSV generation with missing expected columns"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        # Create trades DataFrame with missing columns
        trades_data = {
            'EntryTime': ['2023-01-01 10:00:00'],
            'ExitTime': ['2023-01-01 16:00:00'],
            'Profit': [200.0]
            # Missing: Direction, EntryPrice, ExitPrice, Size, StopLoss, TakeProfit
        }
        trades_df = pd.DataFrame(trades_data)
        
        mock_stats = MagicMock()
        mock_stats.empty = False
        mock_stats._trades = trades_df
        
        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]
        
        result = self.file_gen._generate_trade_csv_files(
            folder, 'DAX', backtest_results
        )
        
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        self.assertTrue(os.path.exists(result[0]))

    def test_generate_trade_csv_write_error(self):
        """Test trade CSV generation with write error"""
        folder = '/nonexistent/invalid/path'
        
        trades_data = {
            'EntryTime': ['2023-01-01 10:00:00'],
            'ExitTime': ['2023-01-01 16:00:00'],
            'Profit': [200.0]
        }
        trades_df = pd.DataFrame(trades_data)
        
        mock_stats = MagicMock()
        mock_stats.empty = False
        mock_stats._trades = trades_df
        
        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]
        
        result = self.file_gen._generate_trade_csv_files(
            folder, 'DAX', backtest_results
        )
        
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 0)

    def test_generate_trade_csv_general_exception(self):
        """Test trade CSV generation with general exception"""
        folder = os.path.join(self.test_dir, 'test_folder')
        os.makedirs(folder, exist_ok=True)
        
        # Create mock stats that will cause exception
        mock_stats = MagicMock()
        mock_stats.empty = False
        mock_stats._trades.copy.side_effect = Exception("General CSV error")
        
        backtest_results = [{
            'backtesting_py_stats': mock_stats
        }]
        
        result = self.file_gen._generate_trade_csv_files(
            folder, 'DAX', backtest_results
        )
        
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 0)




if __name__ == '__main__':
    unittest.main()
