<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>test_heatmap_actual.png.html</title>
    <style>
      html, body {
        box-sizing: border-box;
        display: flow-root;
        height: 100%;
        margin: 0;
        padding: 0;
      }
    </style>
    <script type="text/javascript" src="https://cdn.bokeh.org/bokeh/release/bokeh-3.7.3.min.js"></script>
    <script type="text/javascript">
        Bokeh.set_log_level("info");
    </script>
  </head>
  <body>
    <div id="a31a902e-5f6d-4b05-aad9-d73829f890e3" data-root-id="p2047" style="display: contents;"></div>
  
    <script type="application/json" id="dcbe4592-cb07-4750-ba19-76a6ae5a38e1">
      {"18dd4755-0e30-4fd2-9152-bafe6acdec6a":{"version":"3.7.3","title":"Bokeh Application","roots":[{"type":"object","name":"GridPlot","id":"p2047","attributes":{"rows":null,"cols":null,"toolbar":{"type":"object","name":"Toolbar","id":"p2046","attributes":{"tools":[{"type":"object","name":"ToolProxy","id":"p2042","attributes":{"tools":[{"type":"object","name":"BoxZoomTool","id":"p2018","attributes":{"dimensions":"both","overlay":{"type":"object","name":"BoxAnnotation","id":"p2019","attributes":{"syncable":false,"line_color":"black","line_alpha":1.0,"line_width":2,"line_dash":[4,4],"fill_color":"lightgrey","fill_alpha":0.5,"level":"overlay","visible":false,"left":{"type":"number","value":"nan"},"right":{"type":"number","value":"nan"},"top":{"type":"number","value":"nan"},"bottom":{"type":"number","value":"nan"},"left_units":"canvas","right_units":"canvas","top_units":"canvas","bottom_units":"canvas","handles":{"type":"object","name":"BoxInteractionHandles","id":"p2025","attributes":{"all":{"type":"object","name":"AreaVisuals","id":"p2024","attributes":{"fill_color":"white","hover_fill_color":"lightgray"}}}}}}}}]}},{"type":"object","name":"ToolProxy","id":"p2043","attributes":{"tools":[{"type":"object","name":"ResetTool","id":"p2026"}]}},{"type":"object","name":"SaveTool","id":"p2044"},{"type":"object","name":"ToolProxy","id":"p2045","attributes":{"tools":[{"type":"object","name":"HoverTool","id":"p2028","attributes":{"renderers":"auto","tooltips":[["param1","@param1"],["param2","@param2"],["Value","@_Value{0.[000]}"]]}}]}}],"logo":null}},"children":[[{"type":"object","name":"Figure","id":"p1995","attributes":{"width":400,"height":400,"x_range":{"type":"object","name":"FactorRange","id":"p2004","attributes":{"factors":["1","2","3"]}},"y_range":{"type":"object","name":"FactorRange","id":"p2005","attributes":{"factors":["10","20","30"]}},"x_scale":{"type":"object","name":"CategoricalScale","id":"p2006"},"y_scale":{"type":"object","name":"CategoricalScale","id":"p2007"},"title":{"type":"object","name":"Title","id":"p2002"},"renderers":[{"type":"object","name":"GlyphRenderer","id":"p2039","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p2030","attributes":{"selected":{"type":"object","name":"Selection","id":"p2031","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p2032"},"data":{"type":"map","entries":[["index",{"type":"ndarray","array":{"type":"bytes","data":"AAAAAAEAAAACAAAAAwAAAAQAAAAFAAAABgAAAAcAAAAIAAAA"},"shape":[9],"dtype":"int32","order":"little"}],["param1",{"type":"ndarray","array":["1","1","1","2","2","2","3","3","3"],"shape":[9],"dtype":"object","order":"little"}],["param2",{"type":"ndarray","array":["10","20","30","10","20","30","10","20","30"],"shape":[9],"dtype":"object","order":"little"}],["_Value",{"type":"ndarray","array":{"type":"bytes","data":"AAAAAAAA4D8zMzMzMzPjP2ZmZmZmZuY/mpmZmZmZ6T/NzMzMzMzsPwAAAAAAAPA/mpmZmZmZ8T8zMzMzMzPzP83MzMzMzPQ/"},"shape":[9],"dtype":"float64","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p2040","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p2041"}}},"glyph":{"type":"object","name":"Rect","id":"p2036","attributes":{"x":{"type":"field","field":"param1"},"y":{"type":"field","field":"param2"},"width":{"type":"value","value":1},"height":{"type":"value","value":1},"line_color":{"type":"value","value":null},"fill_color":{"type":"field","field":"_Value","transform":{"type":"object","name":"LinearColorMapper","id":"p1994","attributes":{"palette":["#440154","#440255","#440357","#450558","#45065A","#45085B","#46095C","#460B5E","#460C5F","#460E61","#470F62","#471163","#471265","#471466","#471567","#471669","#47186A","#48196B","#481A6C","#481C6E","#481D6F","#481E70","#482071","#482172","#482273","#482374","#472575","#472676","#472777","#472878","#472A79","#472B7A","#472C7B","#462D7C","#462F7C","#46307D","#46317E","#45327F","#45347F","#453580","#453681","#443781","#443982","#433A83","#433B83","#433C84","#423D84","#423E85","#424085","#414186","#414286","#404387","#404487","#3F4587","#3F4788","#3E4888","#3E4989","#3D4A89","#3D4B89","#3D4C89","#3C4D8A","#3C4E8A","#3B508A","#3B518A","#3A528B","#3A538B","#39548B","#39558B","#38568B","#38578C","#37588C","#37598C","#365A8C","#365B8C","#355C8C","#355D8C","#345E8D","#345F8D","#33608D","#33618D","#32628D","#32638D","#31648D","#31658D","#31668D","#30678D","#30688D","#2F698D","#2F6A8D","#2E6B8E","#2E6C8E","#2E6D8E","#2D6E8E","#2D6F8E","#2C708E","#2C718E","#2C728E","#2B738E","#2B748E","#2A758E","#2A768E","#2A778E","#29788E","#29798E","#287A8E","#287A8E","#287B8E","#277C8E","#277D8E","#277E8E","#267F8E","#26808E","#26818E","#25828E","#25838D","#24848D","#24858D","#24868D","#23878D","#23888D","#23898D","#22898D","#228A8D","#228B8D","#218C8D","#218D8C","#218E8C","#208F8C","#20908C","#20918C","#1F928C","#1F938B","#1F948B","#1F958B","#1F968B","#1E978A","#1E988A","#1E998A","#1E998A","#1E9A89","#1E9B89","#1E9C89","#1E9D88","#1E9E88","#1E9F88","#1EA087","#1FA187","#1FA286","#1FA386","#20A485","#20A585","#21A685","#21A784","#22A784","#23A883","#23A982","#24AA82","#25AB81","#26AC81","#27AD80","#28AE7F","#29AF7F","#2AB07E","#2BB17D","#2CB17D","#2EB27C","#2FB37B","#30B47A","#32B57A","#33B679","#35B778","#36B877","#38B976","#39B976","#3BBA75","#3DBB74","#3EBC73","#40BD72","#42BE71","#44BE70","#45BF6F","#47C06E","#49C16D","#4BC26C","#4DC26B","#4FC369","#51C468","#53C567","#55C666","#57C665","#59C764","#5BC862","#5EC961","#60C960","#62CA5F","#64CB5D","#67CC5C","#69CC5B","#6BCD59","#6DCE58","#70CE56","#72CF55","#74D054","#77D052","#79D151","#7CD24F","#7ED24E","#81D34C","#83D34B","#86D449","#88D547","#8BD546","#8DD644","#90D643","#92D741","#95D73F","#97D83E","#9AD83C","#9DD93A","#9FD938","#A2DA37","#A5DA35","#A7DB33","#AADB32","#ADDC30","#AFDC2E","#B2DD2C","#B5DD2B","#B7DD29","#BADE27","#BDDE26","#BFDF24","#C2DF22","#C5DF21","#C7E01F","#CAE01E","#CDE01D","#CFE11C","#D2E11B","#D4E11A","#D7E219","#DAE218","#DCE218","#DFE318","#E1E318","#E4E318","#E7E419","#E9E419","#ECE41A","#EEE51B","#F1E51C","#F3E51E","#F6E61F","#F8E621","#FAE622","#FDE724"],"nan_color":"white","low":0.5,"high":1.3}}}}},"nonselection_glyph":{"type":"object","name":"Rect","id":"p2037","attributes":{"x":{"type":"field","field":"param1"},"y":{"type":"field","field":"param2"},"width":{"type":"value","value":1},"height":{"type":"value","value":1},"line_color":{"type":"value","value":null},"line_alpha":{"type":"value","value":0.1},"fill_color":{"type":"field","field":"_Value","transform":{"id":"p1994"}},"fill_alpha":{"type":"value","value":0.1},"hatch_alpha":{"type":"value","value":0.1}}},"muted_glyph":{"type":"object","name":"Rect","id":"p2038","attributes":{"x":{"type":"field","field":"param1"},"y":{"type":"field","field":"param2"},"width":{"type":"value","value":1},"height":{"type":"value","value":1},"line_color":{"type":"value","value":null},"line_alpha":{"type":"value","value":0.2},"fill_color":{"type":"field","field":"_Value","transform":{"id":"p1994"}},"fill_alpha":{"type":"value","value":0.2},"hatch_alpha":{"type":"value","value":0.2}}}}}],"toolbar":{"type":"object","name":"Toolbar","id":"p2003","attributes":{"tools":[{"id":"p2018"},{"id":"p2026"},{"type":"object","name":"SaveTool","id":"p2027"},{"id":"p2028"}]}},"toolbar_location":null,"left":[{"type":"object","name":"CategoricalAxis","id":"p2013","attributes":{"ticker":{"type":"object","name":"CategoricalTicker","id":"p2014"},"formatter":{"type":"object","name":"CategoricalTickFormatter","id":"p2015"},"axis_label":"param2","major_label_standoff":0,"major_label_policy":{"type":"object","name":"AllLabels","id":"p2016"},"axis_line_color":null,"major_tick_line_color":null}}],"below":[{"type":"object","name":"CategoricalAxis","id":"p2008","attributes":{"ticker":{"type":"object","name":"CategoricalTicker","id":"p2009"},"formatter":{"type":"object","name":"CategoricalTickFormatter","id":"p2010"},"axis_label":"param1","major_label_standoff":0,"major_label_policy":{"type":"object","name":"AllLabels","id":"p2011"},"axis_line_color":null,"major_tick_line_color":null}}],"center":[{"type":"object","name":"Grid","id":"p2012","attributes":{"axis":{"id":"p2008"},"grid_line_color":null}},{"type":"object","name":"Grid","id":"p2017","attributes":{"dimension":1,"axis":{"id":"p2013"},"grid_line_color":null}},{"type":"object","name":"Label","id":"p2029","attributes":{"text":"Created with Backtesting.py: http://kernc.github.io/backtesting.py","text_color":"silver","text_alpha":0.09,"x":10,"y":15,"x_units":"screen","y_units":"screen"}}]}},0,0]]}}],"callbacks":{"type":"map","entries":[["document_ready",[{"type":"object","name":"CustomJS","id":"p1993","attributes":{"code":"(function() { var i = document.createElement('iframe'); i.style.display='none';i.width=i.height=1;i.loading='eager';i.src='https://kernc.github.io/backtesting.py/plx.gif.html?utm_source='+location.origin;document.body.appendChild(i);})();"}}]]]}}}
    </script>
    <script type="text/javascript">
      (function() {
        const fn = function() {
          Bokeh.safely(function() {
            (function(root) {
              function embed_document(root) {
              const docs_json = document.getElementById('dcbe4592-cb07-4750-ba19-76a6ae5a38e1').textContent;
              const render_items = [{"docid":"18dd4755-0e30-4fd2-9152-bafe6acdec6a","roots":{"p2047":"a31a902e-5f6d-4b05-aad9-d73829f890e3"},"root_ids":["p2047"]}];
              root.Bokeh.embed.embed_items(docs_json, render_items);
              }
              if (root.Bokeh !== undefined) {
                embed_document(root);
              } else {
                let attempts = 0;
                const timer = setInterval(function(root) {
                  if (root.Bokeh !== undefined) {
                    clearInterval(timer);
                    embed_document(root);
                  } else {
                    attempts++;
                    if (attempts > 100) {
                      clearInterval(timer);
                      console.log("Bokeh: ERROR: Unable to run BokehJS code because BokehJS library is missing");
                    }
                  }
                }, 10, root)
              }
            })(window);
          });
        };
        if (document.readyState != "loading") fn();
        else document.addEventListener("DOMContentLoaded", fn);
      })();
    </script>
  </body>
</html>